.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}
.bottom-nav .nav-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #727272;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
}
.bottom-nav .nav-icon svg {
  transition: all 0.3s ease;
}
.bottom-nav .nav-icon:hover {
  color: #1DB954;
  background-color: rgba(29, 185, 84, 0.1);
}
.bottom-nav .nav-icon.active {
  color: #1DB954;
}
.bottom-nav .nav-icon.active svg {
  transform: scale(1.1);
}/*# sourceMappingURL=BottomNav.css.map */