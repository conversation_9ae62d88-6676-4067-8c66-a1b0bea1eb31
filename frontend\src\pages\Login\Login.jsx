import React from "react";
import { Link } from "react-router-dom";
import "./login.css";
import { useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const navigate = useNavigate();
  const [formDate, setFormDate] = useState({
    username: "",
    password: "",
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    axios
      .post(
        "http://localhost:3000/auth/login",
        {
          username: formDate.username,
          password: formDate.password,
        },
        {
          withCredentials: true,
        }
      )
      .then((res) => {
        console.log(res.data);
        navigate("/");
      })
      .catch((err) => {
        console.log(err);
      });
  };

  return (
    <>
      <section className="login-section">
        <h2>Sound Stream</h2>

        <div className="middle">
          <h2>Welcom back</h2>
          <form onSubmit={handleSubmit} action="">
            <input
              name="username"
              type="text"
              placeholder="Username"
              value={formDate.username}
              onChange={(e) =>
                setFormDate({ ...formDate, username: e.target.value })
              }
            />
            <input
              name="password"
              type="password"
              placeholder="Password"
              value={formDate.password}
              onChange={(e) =>
                setFormDate({ ...formDate, password: e.target.value })
              }
            />
            <input type="submit" value="Log In" />
          </form>
        </div>

        <small>
          Don't have an account? <Link to={"/register"}>Register</Link>
        </small>
      </section>
    </>
  );
};

export default Login;
