import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import Home from '../pages/Home/Home'
import Login from '../pages/Login/Login'
import Upload from '../pages/Upload/Upload'
import Register from '../pages/Register/Register'
import Search from '../pages/Search/Search'
import Protected from '../components/Protected'

const AppRouter = () => {
  return (
    <>
      <Router>
        <Routes>
          <Route path="/" element={<Protected><Home /></Protected>} />
          <Route path="/register" element={<Register />} />
          <Route path="/login" element={<Login />} />
          <Route path="/upload" element={<Protected><Upload /></Protected>} />
          <Route path="/search" element={<Protected><Search /></Protected>} />
        </Routes>
      </Router>
    </>
  )
}

export default AppRouter