import React, { useEffect } from "react";
import "./home.css";
import { useDispatch, useSelector } from "react-redux";
import Home2Line from "remixicon-react/Home2LineIcon";
import SearchIcon from "remixicon-react/SearchLineIcon";
import PlayBar from "../../components/PlayBar";
import BottomNav from "../../components/BottomNav";
import { setCurrentSong } from "../../store/songsSlice";
import axios from 'axios'
import { setSongs } from "../../store/songsSlice";


const Home = () => {
    const dispatch = useDispatch();
    const { songs, currentSong } = useSelector((state) => state.songs);

    const handleSongClick = (song) => {
        dispatch(setCurrentSong(song));
    };

    useEffect(() => {
        axios
            .get("http://localhost:3000/songs/get-songs",{
                withCredentials: true,
            })
            .then((res) => {
                console.log(res.data);
                dispatch(setSongs(res.data.songs));
            })
            .catch((err) => {
                console.log(err);
            });
    }, []);

    return (
        <div className="home-container">
            <header className="home-header">
                <span className="music-icon">
                    <Home2Line size={24} />
                </span>
                <span className="stream-title">Stream</span>
                <span className="search-icon">
                    <SearchIcon size={22} />
                </span>
            </header>
            <div className="song-list">
                {songs.map((song) => (
                    <div
                        key={song.id}
                        className={`song-item ${currentSong?.id === song.id ? "active" : ""
                            }`}
                        onClick={() => handleSongClick(song)}
                    >
                        <img className="song-cover" src={song.cover} alt={song.title} />
                        <div className="song-info">
                            <div className="song-title">{song.title}</div>
                            <div className="song-artist">{song.artist}</div>
                        </div>
                    </div>
                ))}
            </div>
            <PlayBar />
            <BottomNav />
        </div>
    );
};

export default Home;
