.upload-container {
  min-height: 100vh;
  background-color: #fff;
  color: #000;
  padding: 20px 20px 140px;
  display: flex;
  flex-direction: column;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;

  .back-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.upload-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.text-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  font-size: 16px;
  outline: none;

  &::placeholder {
    color: #888;
  }

  &:focus {
    border-color: #000;
  }
}

.upload-buttons {
  display: flex;
  gap: 16px;
  margin: 16px 0;
}

.file-upload {
  flex: 1;

  .upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 16px;
    background-color: #f5f5f5;
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    color: #000;
    transition: background-color 0.2s;
    position: relative;

    &:hover {
      background-color: #ebebeb;
    }

    .file-selected {
      position: absolute;
      right: 16px;
      color: #1db954;
    }
  }
}

.submit-button {
  width: 100%;
  padding: 16px;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: auto;
  transition: background-color 0.2s;

  &:hover {
    background-color: #333;
  }

  &:active {
    background-color: #000;
  }
}