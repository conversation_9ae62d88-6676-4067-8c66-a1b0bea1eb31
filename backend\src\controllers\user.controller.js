import userModel from '../models/user.model.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import config from '../config/config.js';


export async function registerUser(req, res, next) {

    try {
        const JWT_SECRET = config.jwt.secret;
        const { username, email, password } = req.body;
        const isUniqueUser = await userModel.findOne({
            username
        });

        if (isUniqueUser) {
            return res.status(400).json({ message: 'Username already exists' });
        }

        const isUniqueUsername = await userModel.findOne({
            username
        });

        if (isUniqueUsername) {
            return res.status(400).json({ message: 'username already exists' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        const token = jwt.sign({ id: userModel._id }, JWT_SECRET, {
            expiresIn: '1day'
        })

        const newUser = await userModel.create({
            username,
            email,
            password: hashedPassword
        });

        res.cookie('token', token)

        res.status(201).json({
            message: 'User registered successfully',
            user: {
                id: newUser._id,
                username: newUser.username,
                email: newUser.email
            },
            token
        })
    } catch (err) {
        next(err)
    }
}

export async function loginUser(req, res, next) {
    try {
        const JWT_SECRET = config.jwt.secret;

        const { username, password } = req.body;

        const user = await userModel.findOne({
            username
        })

        if (!user) {
            return res.status(400).json({ message: 'Invalid username or password' });
        }

        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
            return res.status(400).json({ message: 'Invalid username or password' });
        }

        const token = jwt.sign({ id: user._id }, JWT_SECRET, {
            expiresIn: '1day'
        });

        res.cookie('token', token)

        res.status(200).json({
            message: 'User logged in successfully',
            user: {
                id: user._id,
                username: user.username,
                email: user.email
            },
            token
        });
    } catch (err) {
        next(err)
    }
}

export async function me(req, res, next) {
    try {
        const { token } = req.cookies;
       
        if(!token){
            return res.status(401).json({
                message: 'Unauthorized'
            })
        }

        try {
        const JWT_SECRET = config.jwt.secret;
        const decoded = jwt.verify(token, JWT_SECRET);

        return res.status(200).json({
            message: 'User fetched successfully',
            user: decoded.id
        })
            
        } catch (err) {
            res.status(401).json({
                message: 'Unauthorized'
            })
        }

    } catch (err) {
        next(err)
    }
}
