import React from 'react'
import { <PERSON> } from 'react-router-dom'
import './register.css'
import { useState } from 'react'
import axios from 'axios'
import { useNavigate } from 'react-router-dom'

const Register = () => {

  const navigate = useNavigate();

  const [formDate, setFormDate] = useState({
    username: '',
    email: '',
    password: ''
  })

  const handleSubmit = (e) => {
    e.preventDefault();

    axios
      .post(
        "http://localhost:3000/auth/register",
        {
          username: formDate.username,
          email: formDate.email,
          password: formDate.password,
        },
        {
          withCredentials: true,
        }
      )
      .then((res) => {
        console.log(res.data);
        navigate("/");
      })
      .catch((err) => {
        console.log(err);
      });
  }



  return (
    <>
      <section className='register-section'>
        <h2>Sound Stream</h2>

        <div className="middle">
          <h2>Create new account</h2>
          <form onSubmit={handleSubmit} action="">
            <input 
            type="text" 
            placeholder='Username' 
            name="username"
            value={formDate.username}
            onChange={(e) =>
              setFormDate({ ...formDate, username: e.target.value })
            }
            />
            <input 
            type="email" 
            placeholder='Email' 
            name="email"
            value={formDate.email}
            onChange={(e) =>
              setFormDate({ ...formDate, email: e.target.value })
            }
            />
            <input 
            type="password" 
            placeholder='Password' 
            name="password"
            value={formDate.password}
            onChange={(e) =>
              setFormDate({ ...formDate, password: e.target.value })
            }
            />
            <input 
            type="submit" 
            value='Register' 
            />
          </form>
        </div>

        <small>Already have an account? <Link to={"/login"}>Login</Link></small>
      </section>
    </>
  )
}

export default Register
