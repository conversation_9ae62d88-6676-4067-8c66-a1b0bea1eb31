import express from 'express'
import userRouter from './routers/user.routes.js'
import songRouter from './routers/song.routes.js'
import errorHandler from './middleware/error.handler.js'
import sessionHandler from './middleware/session.handler.js'
import cookieParser from 'cookie-parser'
import cors from 'cors'

const app = express()

app.use(cors({
    origin: 'http://localhost:5173',
    credentials: true
}))

app.use(sessionHandler)
app.use(cookieParser())

app.use(express.json())
app.use('/auth', userRouter);
app.use('/songs', songRouter);

// error handler
app.use(errorHandler);

export default app