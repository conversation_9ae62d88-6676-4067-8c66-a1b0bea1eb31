{"name": "backend", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node --inspect --watch-path=./ ./server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "multer": "^2.0.1", "node-id3": "^0.2.9"}}