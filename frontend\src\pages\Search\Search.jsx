import React, { useState } from "react";
import "./search.css";
import SearchIcon from "remixicon-react/SearchLineIcon";
import BottomNav from "../../components/BottomNav";
import PlayBar from "../../components/PlayBar";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import { setFilteredSongs, selectFilteredSongs } from "../../store/songsSlice";

const Search = () => {
   const dispatch = useDispatch();
    const filteredSongs = useSelector(selectFilteredSongs);
    const currentSong = useSelector(selectCurrentSong);
    const isPlaying = useSelector(selectIsPlaying);
    const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e) => {
    const query = e.target.value;
    console.log(query);

    axios
      .get(`http://localhost:3000/songs/search-song?text=${query}`, {
        withCredentials: true,
      })
      .then((res) => {
        console.log(res.data);
      })
      .catch((err) => {
        console.log(err);
      });

    setSearchQuery(e.target.value);
  };

  

  return (
    <div className="search-page">
      <div className="search-container">
        <div className="search-bar">
          <SearchIcon size={20} className="search-icon" />
          <input
            type="text"
            placeholder="Find in music"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        <div className="search-results">
          {filteredSongs.length > 0 ? (
            <div className="song-list">
              {filteredSongs.map((song) => (
                <div
                  key={song._id}
                  className="song-item"
                  onClick={() => handlePlaySong(song)}
                >
                  <img
                    src={song.poster}
                    alt={song.title}
                    className="song-image"
                  />
                  <div className="song-details">
                    <div className="song-title">{song.title}</div>
                    <div className="song-artist">{song.artist}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : searchQuery ? (
            <p>No results found for "{searchQuery}"</p>
          ) : (
            <p>Type something to search for songs or artists</p>
          )}
        </div>
      </div>
      <PlayBar />
      <BottomNav />
    </div>
  );
};

export default Search;
