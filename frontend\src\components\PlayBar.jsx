import React, { useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PlayIcon from 'remixicon-react/PlayFillIcon';
import PauseIcon from 'remixicon-react/PauseFillIcon';
import { setIsPlaying, setCurrentTime, setDuration } from '../store/songsSlice';
import './PlayBar.css';

const PlayBar = () => {
    const dispatch = useDispatch();
    const { currentSong, isPlaying } = useSelector((state) => state.songs);
    const audioRef = useRef(null);

    useEffect(() => {
        if (currentSong?.file) {
            if (isPlaying) {
                audioRef.current.play();
            } else {
                audioRef.current.pause();
            }
        }
    }, [isPlaying, currentSong]);

    const togglePlay = () => {
        dispatch(setIsPlaying(!isPlaying));
    };

    const handleTimeUpdate = () => {
        dispatch(setCurrentTime(audioRef.current.currentTime));
    };

    const handleLoadedMetadata = () => {
        dispatch(setDuration(audioRef.current.duration));
    };

    if (!currentSong) return null;

    return (
        <div className="play-bar">
            <audio
                ref={audioRef}
                src={currentSong.file}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
            />
            <img className="play-bar-cover" src={currentSong.cover} alt={currentSong.title} />
            <div className="play-bar-info">
                <div className="play-bar-title">{currentSong.title}</div>
                <div className="play-bar-artist">{currentSong.artist}</div>
            </div>
            <button className="play-bar-play" onClick={togglePlay}>
                {isPlaying ? (
                    <PauseIcon size={28} color="#000" />
                ) : (
                    <PlayIcon size={28} color="#000" />
                )}
            </button>
        </div>
    );
};

export default PlayBar;
