.search-page {
  min-height: 100vh;
  background-color: #fff;
  color: white;
  padding: 20px;
  padding-bottom: 80px;
}
.search-page .search-container {
  max-width: 600px;
  margin: 0 auto;
}
.search-page .search-bar {
  background-color: rgba(255, 255, 255, 0.062745098);
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.search-page .search-bar .search-icon {
  color: black;
}
.search-page .search-bar input {
  flex: 1;
  background: none;
  border: none;
  color: black;
  font-size: 16px;
  outline: none;
}
.search-page .search-bar input::-moz-placeholder {
  color: rgba(0, 0, 0, 0.5019607843);
}
.search-page .search-bar input::placeholder {
  color: rgba(0, 0, 0, 0.5019607843);
}
.search-page .search-results {
  padding: 16px 0;
}/*# sourceMappingURL=search.css.map */