import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Home3Line from 'remixicon-react/Home3LineIcon';
import SearchIcon from 'remixicon-react/SearchLineIcon';
import BookmarkIcon from 'remixicon-react/BookmarkLineIcon';
import UserIcon from 'remixicon-react/UserLineIcon';
import './BottomNav.css';

const BottomNav = () => {
    const navigate = useNavigate();
    const location = useLocation();

    const getActiveClass = (path) => {
        return location.pathname === path ? 'nav-icon active' : 'nav-icon';
    };

    return (
        <nav className="bottom-nav">
            <span 
                className={getActiveClass('/')} 
                onClick={() => navigate('/')}
            >
                <Home3Line size={24} />
            </span>
            <span 
                className={getActiveClass('/search')} 
                onClick={() => navigate('/search')}
            >
                <SearchIcon size={24} />
            </span>
            <span 
                className={getActiveClass('/upload')} 
                onClick={() => navigate('/upload')}
            >
                <BookmarkIcon size={24} />
            </span>
            <span 
                className={getActiveClass('/login')} 
                onClick={() => navigate('/login')}
            >
                <UserIcon size={24} />
            </span>
        </nav>
    );
};

export default BottomNav;
