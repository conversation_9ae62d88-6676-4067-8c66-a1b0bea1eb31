.home-container {
  background: #d7d7d7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 110px;
}

.home-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #fff;

  .music-icon,
  .search-icon {
    font-size: 1.3rem;
    color: #222;
  }

  .stream-title {
    font-weight: 700;
    color: #222;
  }
}

.song-list {
  flex: 1;
  margin-bottom: 16px;
}

.song-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 16px;
  margin: 4px 12px;

  .song-cover {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    object-fit: cover;
    margin-right: 16px;
    background: #eee;
  }

  .song-info {
    .song-title {
      font-weight: 600;
      color: #222;
    }

    .song-artist {
      font-size: 0.95rem;
      color: #888;
      margin-top: 2px;
      text-overflow: ellipsis;
      hyphens: manual;
            overflow-wrap: break-word;


      overflow: hidden;
      // white-space: nowrap;
    }
  }

  .play-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 64px;
    background: #f7f7f7;
    border-radius: 16px;
    margin: 0 10px;
    display: flex;
    align-items: center;
    padding: 12px 18px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    z-index: 10;
    .play-bar-cover {
      width: 48px;
      height: 48px;
      border-radius: 10px;
      object-fit: cover;
      margin-right: 14px;
      background: #eee;
    }
    .play-bar-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .play-bar-title {
        font-size: 1.08rem;
        font-weight: 600;
        color: #222;
      }
      .play-bar-artist {
        font-size: 0.95rem;
        color: #888;
        margin-top: 2px;
      }
    }
    .play-bar-play {
      background: none;
      border: none;
      outline: none;
      cursor: pointer;
      margin-left: 10px;
      svg {
        display: block;
      }
    }
  }

  .bottom-nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 56px;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.04);
    z-index: 20;
    .nav-icon {
      font-size: 1.5rem;
      color: #bbb;
      &.active {
        color: #000;
      }
    }
  }
}
.song-item:focus {
  background-color: transparent;
}
