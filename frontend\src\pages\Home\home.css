.home-container {
  background: #d7d7d7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 110px;
}

.home-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #fff;
}
.home-header .music-icon,
.home-header .search-icon {
  font-size: 1.3rem;
  color: #222;
}
.home-header .stream-title {
  font-weight: 700;
  color: #222;
}

.song-list {
  flex: 1;
  margin-bottom: 16px;
}

.song-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 16px;
  margin: 4px 12px;
}
.song-item .song-cover {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  -o-object-fit: cover;
     object-fit: cover;
  margin-right: 16px;
  background: #eee;
}
.song-item .song-info .song-title {
  font-weight: 600;
  color: #222;
}
.song-item .song-info .song-artist {
  font-size: 0.95rem;
  color: #888;
  margin-top: 2px;
  text-overflow: ellipsis;
  -webkit-hyphens: manual;
          hyphens: manual;
  overflow-wrap: break-word;
  overflow: hidden;
}
.song-item .play-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 64px;
  background: #f7f7f7;
  border-radius: 16px;
  margin: 0 10px;
  display: flex;
  align-items: center;
  padding: 12px 18px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  z-index: 10;
}
.song-item .play-bar .play-bar-cover {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  -o-object-fit: cover;
     object-fit: cover;
  margin-right: 14px;
  background: #eee;
}
.song-item .play-bar .play-bar-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.song-item .play-bar .play-bar-info .play-bar-title {
  font-size: 1.08rem;
  font-weight: 600;
  color: #222;
}
.song-item .play-bar .play-bar-info .play-bar-artist {
  font-size: 0.95rem;
  color: #888;
  margin-top: 2px;
}
.song-item .play-bar .play-bar-play {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  margin-left: 10px;
}
.song-item .play-bar .play-bar-play svg {
  display: block;
}
.song-item .bottom-nav {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 56px;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.04);
  z-index: 20;
}
.song-item .bottom-nav .nav-icon {
  font-size: 1.5rem;
  color: #bbb;
}
.song-item .bottom-nav .nav-icon.active {
  color: #000;
}

.song-item:focus {
  background-color: transparent;
}/*# sourceMappingURL=home.css.map */