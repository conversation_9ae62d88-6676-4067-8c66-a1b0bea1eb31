export default function errorHandler(err, req, res, next) {
    // If response headers already sent, delegate to default Express handler
    if (res.headersSent) {
        return next(err);
    }

    // Custom statusCode (e.g., err.statusCode = 404) or fallback 500
    const statusCode = err.statusCode || 500;

    // Log the error for server-side debugging
    console.error(err);

    const responsePayload = {
        message: err.message || 'Internal Server Error',
    };

    // Include stack trace only in development mode
    if (process.env.NODE_ENV === 'development') {
        responsePayload.stack = err.stack;
    }

    res.status(statusCode).json(responsePayload);
}