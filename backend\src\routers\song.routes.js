import express from 'express'
import { uploadSong, getSongs , getSong, searchSong , uploadAllSong } from '../controllers/song.controller.js'
import multer from 'multer';
import jwt from 'jsonwebtoken';
import config from '../config/config.js';

const JWT_SECRET = config.jwt.secret;

const router = express.Router()


router.use((req, res, next)=>{
    const {token} = req.cookies;
    if (!token) {
        res.status(401).json({
            message: 'Unauthorized'
        })
    } else {
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            next();
        } catch (error) {
            res.status(401).json({
            message: 'Unauthorized'
        })
        }
    }
})


const storage = multer.memoryStorage();
const uploadMiddleware = multer({ storage: storage });


router.post("/upload",
    uploadMiddleware.single('audioFile'),
    uploadSong
)
router.post("/upload-all",
    uploadMiddleware.array('audioFile', 100),
    uploadAllSong
)

router.get("/get-songs",
    getSongs
)

router.get("/get-song/:id",
    getSong
)

router.get("/search-song",
    searchSong
)

export default router
