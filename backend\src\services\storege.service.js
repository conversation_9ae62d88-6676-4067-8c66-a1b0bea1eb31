import ImageKit from "imagekit";
import config from '../config/config.js';


let imagekit = new ImageKit({
    publicKey: config.imagekit.publicKey,
    privateKey: config.imagekit.privateKey,
    urlEndpoint: config.imagekit.urlEndpoint
});

export function uploadFile(file) {
    return new Promise((resolve, reject) => {
        imagekit.upload({
            file: file,
            fileName: "audio-file-" + Date.now() + ".mp3",
            folder: "/audio-files/"
        }, function (error, result) {
            if (error) {
                reject(error);
            } else {
                resolve(result);
            }
        });
    })
}