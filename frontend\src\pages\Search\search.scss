.search-page {
    min-height: 100vh;
    background-color: #fff;
    color: white;
    padding: 20px;
    padding-bottom: 80px; // Space for bottom nav

    .search-container {
        max-width: 600px;
        margin: 0 auto;
    }

    .search-bar {
        background-color: #ffffff10;
        border-radius: 8px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .search-icon {
            color: black;
        }

        input {
            flex: 1;
            background: none;
            border: none;
            color:  black;
            font-size: 16px;
            outline: none;

            &::placeholder {
                color: #00000080;
            }
        }
    }
    .search-results {
        padding: 16px 0;
    }
}
