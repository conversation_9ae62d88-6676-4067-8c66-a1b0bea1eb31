import mongoose from 'mongoose'

const songSchema = new mongoose.Schema({
    title: { type: String, required: true, unique: true },
    artist: { type: String, required: true },
    cover: { type: String, default: 'https://imgs.search.brave.com/hF0hQq3D-tV8KicOKteVbFDr0jgIXfEDFBMdy7q09mE/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly9jZG4u/aWNvbnNjb3V0LmNv/bS9pY29uL2ZyZWUv/cG5nLTI1Ni9mcmVl/LW11c2ljLWljb24t/ZG93bmxvYWQtaW4t/c3ZnLXBuZy1naWYt/ZmlsZS1mb3JtYXRz/LS1iZWF1dGlmdWwt/c2luZ2xlLWNvbG9y/LWljb25zLXBhY2st/bWlzY2VsbGFuZW91/cy00NjAxODkucG5n/P2Y9d2VicCZ3PTEy/OA' },
    file: { type: String, required: true },
})

const SongModel = mongoose.model('Song', songSchema)

export default SongModel