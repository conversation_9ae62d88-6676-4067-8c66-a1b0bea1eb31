import React from 'react'
import axios from 'axios'
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom'

const Protected = ({children}) => {
    const navigate = useNavigate();

    useEffect(() => {
        axios
            .get("http://localhost:3000/auth/me",{
                withCredentials: true,
            })
            .then((res) => {
                console.log(res.data);
            })
            .catch(() => {
                navigate("/login");
            });
    }, []);
  return children;
}

export default Protected
