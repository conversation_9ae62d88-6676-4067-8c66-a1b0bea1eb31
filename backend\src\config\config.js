import dotenv from 'dotenv';

dotenv.config();

const _config = {
    imagekit: {
        publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
        privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
        urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT
    },
    jwt: {
        secret: process.env.JWT_SECRET_KEY
    },
    session: {
        secret: process.env.SESSION_SECRET_KEY
    }
}

export default _config;