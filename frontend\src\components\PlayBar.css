.play-bar {
  max-width: 50vmax;
  position: fixed;
  bottom: 70px;
  z-index: 10;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}
.play-bar .play-bar-cover {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  -o-object-fit: cover;
     object-fit: cover;
}
.play-bar .play-bar-info {
  flex: 1;
  min-width: 0;
}
.play-bar .play-bar-info .play-bar-title {
  color: #181818;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.play-bar .play-bar-info .play-bar-artist {
  color: #b3b3b3;
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.play-bar .play-bar-play {
  background-color: #1db954;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.play-bar .play-bar-play:hover {
  background-color: #1ed760;
  transform: scale(1.05);
}
.play-bar .play-bar-play svg {
  color: black;
}/*# sourceMappingURL=PlayBar.css.map */