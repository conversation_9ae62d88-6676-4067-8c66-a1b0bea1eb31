import songModel from '../models/song.model.js'
import { uploadFile } from '../services/storege.service.js'
import NodeID3  from 'node-id3';

export async function uploadSong(req, res, next) {
    try {
        // console.log(req.file)
        const result = await uploadFile(req.file.buffer)
        const { title, artist } = req.body;
        const audioUrl = result.url

        const song = await songModel.create({
            title,
            artist,
            file: audioUrl
        });

        res.status(201).json({
            message: 'Song uploaded successfully',
            song: {
                id: song._id,
                title: song.title,
                artist: song.artist,
                audio: song.audio
            }
        })

    } catch (err) {
        next(err)
    }
}

export async function uploadAllSong(req, res, next) {
    try {
        const files = req.files; // Array of files
        const { title, artist } = req.body; // Arrays or single values

        if (!files || !files.length) {
            return res.status(400).json({ message: 'No files uploaded.' });
        }

        // Ensure title and artist are arrays
        const titles = Array.isArray(title) ? title : [title];
        const artists = Array.isArray(artist) ? artist : [artist];

        const uploadedSongs = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Read ID3 tags from the buffer
            const tags = NodeID3.read(file.buffer);

            // Use provided title/artist, or fallback to ID3 tags, or default
            const songTitle = titles[i] || tags.title || `Untitled ${i + 1}`;
            const songArtist = artists[i] || tags.artist || `Unknown Artist`;

            // Upload to ImageKit (or your chosen service)
            const result = await uploadFile(file.buffer);

            // Save song to the database
            const song = await songModel.create({
                title: songTitle,
                artist: songArtist,
                file: result.url
            });

            uploadedSongs.push({
                id: song._id,
                title: song.title,
                artist: song.artist,
                audio: song.file
            });
        }

        res.status(201).json({
            message: 'Songs uploaded successfully',
            songs: uploadedSongs
        });
    } catch (err) {
        next(err)
    }
};



export async function getSongs(req, res, next) {
    try {
        const songs = await songModel.find()

        res.status(200).json({
            message: 'Songs fetched successfully',
            songs: songs
        })
    } catch (err) {
        next(err)
    }
}

export async function getSong(req, res, next) {
    const songId = req.params.id;
    try {
        const song = await songModel.findOne({
            _id: songId
        });
        res.status(200).json({
            message: 'Song fetched successfully',
            song
        })
    } catch (err) {
        next(err)
    }
}

export async function searchSong(req, res, next) {
    const text = req.query.text;
    try {
        const songs = await songModel.find({
            title: { $regex: text, $options: 'i' }
        });
        res.status(200).json({
            message: 'Songs fetched successfully',
            songs
        })
    } catch (err) {
        next(err)
    }
}

