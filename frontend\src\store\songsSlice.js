import { createSlice } from '@reduxjs/toolkit';
import { defaultSongs } from './defaultSongs';

const initialState = {
    songs: defaultSongs,
    currentSong: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
};

const songsSlice = createSlice({
    name: 'songs',
    initialState,
    reducers: {
        setSongs: (state, action) => {
            state.songs = action.payload;
        },
        setCurrentSong: (state, action) => {
            state.currentSong = action.payload;
        },
        setIsPlaying: (state, action) => {
            state.isPlaying = action.payload;
        },
        setCurrentTime: (state, action) => {
            state.currentTime = action.payload;
        },
        setDuration: (state, action) => {
            state.duration = action.payload;
        },
        
        setFilteredSongs: (state, action) => {
            state.filteredSongs = action.payload;
        }
    },
});

export const {
    setSongs,
    setCurrentSong,
    setIsPlaying,
    setCurrentTime,
    setDuration,
    setFilteredSongs,
} = songsSlice.actions;

export default songsSlice.reducer;

export const selectSongs = (state) => state.songs.songs;
export const selectCurrentSong = (state) => state.songs.currentSong;
export const selectIsPlaying = (state) => state.songs.isPlaying;
export const selectFilteredSongs = (state) => state.songs.filteredSongs;
