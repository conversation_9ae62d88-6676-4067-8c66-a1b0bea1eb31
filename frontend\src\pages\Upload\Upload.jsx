import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import './upload.css'
import BottomNav from '../../components/BottomNav'
import PlayBar from '../../components/PlayBar'
import { setSongs, setCurrentSong } from '../../store/songsSlice'

const Upload = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { songs } = useSelector((state) => state.songs);
  const [songData, setSongData] = useState({
    title: '',
    artist: '',
    audioFile: null,
    imageFile: null
  })

  const handleInputChange = (e) => {
    setSongData({
      ...songData,
      [e.target.name]: e.target.value
    })
  }

  const handleAudioUpload = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSongData({
        ...songData,
        audioFile: e.target.files[0]
      })
    }
  }

  const handleImageUpload = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSongData({
        ...songData,
        imageFile: e.target.files[0]
      })
    }
  }

  const handleSubmit = () => {
    if (!songData.audioFile) {
      alert('Please upload an audio file')
      return
    }

    const newSong = {
      id: songs.length + 1,
      title: songData.title || songData.audioFile.name.replace(/\.[^/.]+$/, ""),
      artist: songData.artist || 'Unknown Artist',
      cover: songData.imageFile ? URL.createObjectURL(songData.imageFile) : 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=64&h=64',
      file: URL.createObjectURL(songData.audioFile)
    }

    dispatch(setSongs([...songs, newSong]))
    dispatch(setCurrentSong(newSong))
    navigate('/')
  }

  return (
    <div className="upload-container">
      <div className="upload-header">
        <button className="back-button" onClick={() => navigate(-1)}>←</button>
        <h1>Upload Music</h1>
      </div>

      <div className="upload-content">
        <input
          type="text"
          placeholder="Song Title"
          name="title"
          value={songData.title}
          onChange={handleInputChange}
          className="text-input"
        />

        <input
          type="text"
          placeholder="Artist Name"
          name="artist"
          value={songData.artist}
          onChange={handleInputChange}
          className="text-input"
        />

        <div className="upload-buttons">
          <div className="file-upload">
            <input
              type="file"
              id="audio-upload"
              accept="audio/*"
              onChange={handleAudioUpload}
              style={{ display: 'none' }}
            />
            <label htmlFor="audio-upload" className="upload-button">
              Upload Audio File
              {songData.audioFile && <span className="file-selected">✓</span>}
            </label>
          </div>

          <div className="file-upload">
            <input
              type="file"
              id="image-upload"
              accept="image/*"
              onChange={handleImageUpload}
              style={{ display: 'none' }}
            />
            <label htmlFor="image-upload" className="upload-button">
              Upload image File
              {songData.imageFile && <span className="file-selected">✓</span>}
            </label>
          </div>
        </div>

        <button className="submit-button" onClick={handleSubmit}>
          Upload Music
        </button>
      </div>
      
      
      <BottomNav />
    </div>
  )
}

export default Upload
